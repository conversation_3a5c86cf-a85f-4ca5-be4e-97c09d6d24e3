/**
 * 统一的日期格式化工具
 * 确保服务端和客户端输出一致，避免水合不匹配
 */

// 使用固定的 locale 和 timezone，确保服务端和客户端一致
const DATE_LOCALE = 'en-GB'; // DD/MM/YYYY 格式
const DATETIME_LOCALE = 'en-GB'; // DD/MM/YYYY, HH:mm:ss 格式
const TIMEZONE = 'UTC'; // 使用 UTC 避免时区差异

/**
 * 格式化日期为 DD/MM/YYYY 格式
 * 用于邮件列表中的日期显示
 */
export function formatEmailDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(DATE_LOCALE, {
      timeZone: TIMEZONE,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
}

/**
 * 格式化日期时间为 DD/MM/YYYY, HH:mm:ss 格式
 * 用于邮件详情中的完整日期时间显示
 */
export function formatEmailDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(DATETIME_LOCALE, {
      timeZone: TIMEZONE,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }).format(date);
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return 'Invalid Date';
  }
}

/**
 * 格式化为相对时间（如：2 hours ago）
 * 可选的高级格式化功能
 */
export function formatRelativeTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      // 超过一周，显示具体日期
      return formatEmailDate(dateString);
    }
  } catch (error) {
    console.error('Error formatting relative time:', error);
    return formatEmailDate(dateString);
  }
}
