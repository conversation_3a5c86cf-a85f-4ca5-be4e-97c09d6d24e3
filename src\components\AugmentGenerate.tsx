"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

interface GeneratedEmail {
  id: number;
  email: string;
  createdAt: string;
}

interface EmailMessage {
  id: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  snippet: string;
  isRead: boolean;
}

interface VerificationCode {
  code: string;
  email: string;
  subject: string;
  date: string;
}

export default function AugmentGenerate() {
  const [generatedEmail, setGeneratedEmail] = useState<GeneratedEmail | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emails, setEmails] = useState<EmailMessage[]>([]);
  const [emailsLoading, setEmailsLoading] = useState(false);
  const [verificationCodes, setVerificationCodes] = useState<
    VerificationCode[]
  >([]);
  const [copiedEmail, setCopiedEmail] = useState(false);
  const [copiedCodes, setCopiedCodes] = useState<Set<string>>(new Set());
  const [monitoringInterval, setMonitoringInterval] =
    useState<NodeJS.Timeout | null>(null);
  const { data: session } = useSession();

  // 生成新邮箱
  const generateEmail = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/generate-email", {
        method: "POST",
      });
      const data = await response.json();

      if (data.success) {
        setGeneratedEmail(data.email);
        // 开始监控邮件
        startEmailMonitoring(data.email.email);
      } else {
        setError(data.error || "生成邮箱失败");
      }
    } catch (error) {
      console.error("Error generating email:", error);
      setError("网络错误，请重试");
    } finally {
      setLoading(false);
    }
  };

  // 停止邮件监控
  const stopEmailMonitoring = () => {
    if (monitoringInterval) {
      clearInterval(monitoringInterval);
      setMonitoringInterval(null);
    }
  };

  // 开始邮件监控
  const startEmailMonitoring = (email: string) => {
    // 先停止之前的监控
    stopEmailMonitoring();

    // 立即加载一次
    loadEmailsForAddress(email);

    // 每3秒刷新一次
    const interval = setInterval(() => {
      loadEmailsForAddress(email);
    }, 3000);

    setMonitoringInterval(interval);
  };

  // 加载指定邮箱的邮件
  const loadEmailsForAddress = async (email: string) => {
    setEmailsLoading(true);
    try {
      const response = await fetch(
        `/api/emails-for-address?email=${encodeURIComponent(email)}`
      );
      const data = await response.json();

      if (data.success) {
        const newEmails = data.emails || [];
        // 合并新邮件，避免重复
        setEmails((prevEmails) => {
          const existingIds = new Set(prevEmails.map((e) => e.id));
          const uniqueNewEmails = newEmails.filter(
            (e) => !existingIds.has(e.id)
          );
          const allEmails = [...prevEmails, ...uniqueNewEmails];
          // 提取验证码（使用所有邮件）
          extractVerificationCodes(allEmails);
          return allEmails;
        });
      }
    } catch (error) {
      console.error("Error loading emails for address:", error);
    } finally {
      setEmailsLoading(false);
    }
  };

  // 提取验证码
  const extractVerificationCodes = (emails: EmailMessage[]) => {
    const codes: VerificationCode[] = [];
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    emails.forEach((email) => {
      const emailDate = new Date(email.date);

      // 检查是否在5分钟内收到
      if (emailDate >= fiveMinutesAgo) {
        // 检查标题或内容是否包含关键词
        const hasWelcome = email.subject
          .toLowerCase()
          .includes("welcome to augment code");
        const hasVerificationCode = email.snippet
          .toLowerCase()
          .includes("verification code");

        if (hasWelcome || hasVerificationCode) {
          // 首先尝试匹配 "verification code is: 123456" 格式
          let codeMatch = email.snippet.match(
            /verification code is:\s*(\d{6})/i
          );

          if (!codeMatch) {
            // 如果没找到，搜索任何连续的6位数字
            codeMatch = email.snippet.match(/\b(\d{6})\b/);
          }

          if (codeMatch) {
            const code = codeMatch[1];
            codes.push({
              code,
              email: email.to,
              subject: email.subject,
              date: email.date,
            });
          }
        }
      }
    });

    // 按时间排序，最新的在前
    codes.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    // 合并新验证码，避免重复
    setVerificationCodes((prevCodes) => {
      const existingCodes = new Set(
        prevCodes.map((c) => `${c.code}-${c.date}`)
      );
      const uniqueNewCodes = codes.filter(
        (c) => !existingCodes.has(`${c.code}-${c.date}`)
      );
      const allCodes = [...prevCodes, ...uniqueNewCodes];
      // 重新排序
      return allCodes.sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );
    });
  };

  // 复制邮箱到剪贴板
  const copyEmailToClipboard = async (email: string) => {
    try {
      await navigator.clipboard.writeText(email);
      setCopiedEmail(true);
      setTimeout(() => setCopiedEmail(false), 2000); // 2秒后恢复
    } catch (error) {
      console.error("Failed to copy email:", error);
    }
  };

  // 复制验证码到剪贴板
  const copyCodeToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCodes((prev) => new Set([...prev, code]));
      setTimeout(() => {
        setCopiedCodes((prev) => {
          const newSet = new Set(prev);
          newSet.delete(code);
          return newSet;
        });
      }, 2000); // 2秒后恢复
    } catch (error) {
      console.error("Failed to copy code:", error);
    }
  };

  // 页面加载时自动生成邮箱
  useEffect(() => {
    if (session) {
      generateEmail();
    }
  }, [session]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      stopEmailMonitoring();
    };
  }, []);

  // 监听页面可见性变化，当页面隐藏时停止监控，显示时恢复
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时停止监控
        stopEmailMonitoring();
      } else if (generatedEmail) {
        // 页面显示时恢复监控
        startEmailMonitoring(generatedEmail.email);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      stopEmailMonitoring();
    };
  }, [generatedEmail]);

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Augment Generate
        </h2>

        {/* 生成的邮箱 */}
        <div className="bg-white shadow rounded-lg p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">生成的邮箱</h3>
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-gray-600">生成中...</span>
            </div>
          ) : error ? (
            <div className="text-red-600 mb-4">
              {error}
              <button
                onClick={generateEmail}
                className="ml-4 bg-blue-600 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm"
              >
                重试
              </button>
            </div>
          ) : generatedEmail ? (
            <div className="flex items-center space-x-3">
              <span className="text-lg font-mono bg-gray-100 px-3 py-2 rounded">
                {generatedEmail.email}
              </span>
              <button
                onClick={() => copyEmailToClipboard(generatedEmail.email)}
                className={`text-white text-sm font-medium py-2 px-4 rounded-md transition-all duration-200 ${
                  copiedEmail
                    ? "bg-green-500 hover:bg-green-600"
                    : "bg-blue-500 hover:bg-blue-600"
                }`}
              >
                {copiedEmail ? "已复制!" : "复制"}
              </button>
              <button
                onClick={generateEmail}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                重新生成
              </button>
            </div>
          ) : null}
        </div>

        {/* 验证码提取 */}
        {verificationCodes.length > 0 && (
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">验证码</h3>
            <div className="space-y-3">
              {verificationCodes.map((vc, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-yellow-50 p-3 rounded"
                >
                  <div>
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl font-mono font-bold text-green-600">
                        {vc.code}
                      </span>
                      <button
                        onClick={() => copyCodeToClipboard(vc.code)}
                        className={`text-white text-xs font-medium py-1 px-3 rounded-md transition-all duration-200 ${
                          copiedCodes.has(vc.code)
                            ? "bg-blue-500 hover:bg-blue-600"
                            : "bg-green-500 hover:bg-green-600"
                        }`}
                      >
                        {copiedCodes.has(vc.code) ? "已复制!" : "复制"}
                      </button>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      来自: {vc.subject} | {new Date(vc.date).toLocaleString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 邮件监控 */}
        {generatedEmail && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                邮件监控 ({emails.length})
              </h3>
              {emailsLoading && (
                <div className="flex items-center text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  刷新中...
                </div>
              )}
            </div>

            {emails.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                暂无邮件，每3秒自动刷新...
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {emails.map((email) => (
                  <div
                    key={email.id}
                    className="border border-gray-200 rounded p-3"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          {email.subject}
                        </div>
                        <div className="text-sm text-gray-600">
                          From: {email.from}
                        </div>
                        <div className="text-sm text-gray-500 mt-1">
                          {email.snippet}
                        </div>
                      </div>
                      <div className="text-sm text-gray-500 ml-4">
                        {new Date(email.date).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
