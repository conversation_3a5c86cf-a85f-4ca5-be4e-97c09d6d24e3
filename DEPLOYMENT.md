# One Mail 部署指南

## 概述

One Mail 是一个基于 Next.js 和 Keycloak 的邮件管理应用程序，支持 SSR（服务端渲染）和 JWT 认证。

## 认证架构

- **NextAuth.js**: 作为中间层与 Keycloak 对接
- **授权码模式**: 向 Keycloak 换取 access_token、refresh_token、id_token
- **Token 管理**: 
  - Refresh_token 存储在服务端（httpOnly cookie）
  - Access_token 用于 SSR 调用后端 API，有效期短（2-5 分钟）
  - 自动刷新机制，到期时用 refresh_token 静默刷新
- **角色验证**: 验证用户是否有 `one-mail-admin` 角色

## 本地开发

1. **安装依赖**:
   ```bash
   npm install
   ```

2. **配置环境变量**:
   - 复制 `.env.local` 文件并根据需要修改
   - 确保 Keycloak 配置正确

3. **运行开发服务器**:
   ```bash
   npm run dev
   ```

4. **访问应用**:
   - 本地地址: http://localhost:9042

## 生产部署

### 1. 服务器配置

确保服务器满足以下要求：
- Node.js 18+ 
- PostgreSQL 17 (如果使用数据库)
- 反向代理 (Nginx/Apache)

### 2. 环境变量配置

复制 `.env.production.example` 到 `.env.local` 并更新以下关键配置：

```bash
# 重要：更新为你的生产域名
NEXTAUTH_URL=https://your-domain.com
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# 生成安全的密钥
NEXTAUTH_SECRET=your-secure-random-secret-key

# Keycloak 配置
KEYCLOAK_CLIENT_SECRET=your-production-client-secret
```

### 3. 域名配置注意事项

**重要**: 由于应用将部署在服务器的 localhost:9042，但通过域名访问，需要正确配置：

1. **Keycloak 重定向 URI**: 
   - 在 Keycloak 客户端配置中添加: `https://your-domain.com/*`
   - 确保包含生产域名的回调 URL

2. **环境变量**:
   ```bash
   # 使用生产域名，不是 localhost
   NEXTAUTH_URL=https://your-domain.com
   NEXT_PUBLIC_BASE_URL=https://your-domain.com
   ```

3. **反向代理配置** (Nginx 示例):
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:9042;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

### 4. 构建和启动

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm run start
```

### 5. 进程管理

建议使用 PM2 管理 Node.js 进程：

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "one-mail" -- start

# 保存 PM2 配置
pm2 save
pm2 startup
```

## 安全配置

### JWT 验证中间件

应用包含以下安全特性：

1. **API 路由保护**: 所有 API 调用都需要有效的 JWT
2. **角色验证**: 验证用户是否有 `one-mail-admin` 角色
3. **自动重定向**: 无权限用户自动重定向到登录页面
4. **Token 刷新**: 自动处理 token 过期和刷新

### 中间件配置

`middleware.ts` 保护所有路由，除了：
- `/api/auth/*` (NextAuth API)
- `/auth/*` (认证页面)
- 静态资源

## 故障排除

### 常见问题

1. **Token 验证失败**:
   - 检查 Keycloak JWKS 端点是否可访问
   - 验证客户端 ID 和密钥

2. **角色验证失败**:
   - 确认用户在 Keycloak 中有 `one-mail-admin` 角色
   - 检查角色映射配置

3. **重定向循环**:
   - 验证 `NEXTAUTH_URL` 配置
   - 检查 Keycloak 重定向 URI 设置

4. **CORS 错误**:
   - 确保 Keycloak 允许来自你域名的请求
   - 检查反向代理配置

### 日志监控

应用会记录以下关键事件：
- 认证成功/失败
- Token 刷新
- 角色验证结果
- API 访问日志

## 维护

### Token 管理

- Access token 有效期: 2-5 分钟
- Refresh token 有效期: 30 天
- 自动刷新机制确保用户体验流畅

### 角色变更

当用户角色在 Keycloak 中变更时：
1. 可通过 Keycloak Admin API 注销用户 session
2. 强制用户重新登录获取新角色
3. 所有 token 立即失效

## 支持

如有问题，请检查：
1. 服务器日志
2. 浏览器开发者工具
3. Keycloak 服务器日志
4. 反向代理日志
