import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import jwt from "jsonwebtoken";
import { createRemoteJWKSet, jwtVerify } from "jose";

// 创建 JWKS 客户端用于验证 JWT
const JWKS = createRemoteJWKSet(
  new URL(`${process.env.KEYCLOAK_ISSUER_URL}/protocol/openid-connect/certs`)
);

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email?: string;
    name?: string;
    roles: string[];
    hasRequiredRole: boolean;
  };
}

// 从 Keycloak JWT 中提取角色
function extractRoles(payload: any): string[] {
  const roles: string[] = [];

  // 从 realm_access 中提取角色
  if (payload.realm_access?.roles) {
    roles.push(...payload.realm_access.roles);
  }

  // 从 resource_access 中提取特定客户端的角色
  const clientId = process.env.KEYCLOAK_ROLE_CLIENT_ID || process.env.KEYCLOAK_CLIENT_ID;
  if (clientId && payload.resource_access?.[clientId]?.roles) {
    roles.push(...payload.resource_access[clientId].roles);
  }

  return roles;
}

// 检查用户是否有必需的角色
function hasRequiredRole(roles: string[]): boolean {
  const requiredRole = process.env.KEYCLOAK_REQUIRED_ROLE;
  if (!requiredRole) return true;
  return roles.includes(requiredRole);
}

// 验证 JWT 令牌
async function verifyJWT(token: string) {
  try {
    // 不在 jose 层面强制校验 audience，因为 Keycloak 的 aud 可能为数组或 'account'
    const { payload } = await jwtVerify(token, JWKS, {
      issuer: process.env.KEYCLOAK_ISSUER_URL,
    });

    // 进行更灵活且安全的客户端校验：满足以下其一即可
    // 1) aud 包含 KEYCLOAK_CLIENT_ID（字符串或数组）
    // 2) azp 等于 KEYCLOAK_CLIENT_ID（Keycloak 常见的“authorized party”字段）
    const expected = process.env.KEYCLOAK_CLIENT_ID;
    if (expected) {
      const aud = (payload as any).aud as string | string[] | undefined;
      const azp = (payload as any).azp as string | undefined;

      const audMatches = typeof aud === "string" ? aud === expected : Array.isArray(aud) ? aud.includes(expected) : false;
      const azpMatches = azp === expected;

      if (!audMatches && !azpMatches) {
        console.warn("JWT audience/azp mismatch", { aud, azp, expected });
        return null;
      }
    }

    return payload;
  } catch (error) {
    console.error("JWT verification failed:", error);
    return null;
  }
}

// JWT 验证中间件
export async function withAuth(
  request: NextRequest,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // 首先尝试从 NextAuth session 获取令牌
    const sessionToken = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    let accessToken: string | null = null;
    let payload: any = null;

    if (sessionToken?.accessToken) {
      // 使用 session 中的访问令牌
      accessToken = sessionToken.accessToken;
      payload = await verifyJWT(accessToken);
    } else {
      // 尝试从 Authorization header 获取令牌
      const authHeader = request.headers.get("authorization");
      if (authHeader?.startsWith("Bearer ")) {
        accessToken = authHeader.substring(7);
        payload = await verifyJWT(accessToken);
      }
    }

    if (!payload) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Invalid or missing token" },
        { status: 401 }
      );
    }

    // 提取用户信息和角色
    const roles = extractRoles(payload);
    const userHasRequiredRole = hasRequiredRole(roles);

    // 检查是否有必需的角色
    if (!userHasRequiredRole) {
      return NextResponse.json(
        {
          error: "Forbidden",
          message: `Missing required role: ${process.env.KEYCLOAK_REQUIRED_ROLE}`,
          redirectTo: "/auth/signin?error=insufficient_role"
        },
        { status: 403 }
      );
    }

    // 将用户信息添加到请求对象
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.user = {
      id: payload.sub as string,
      email: payload.email as string,
      name: payload.name as string || payload.preferred_username as string,
      roles,
      hasRequiredRole: userHasRequiredRole,
    };

    return handler(authenticatedRequest);
  } catch (error) {
    console.error("Auth middleware error:", error);
    return NextResponse.json(
      { error: "Internal Server Error", message: "Authentication failed" },
      { status: 500 }
    );
  }
}

// 用于页面级别的认证检查
export async function requireAuth(request: NextRequest) {
  const sessionToken = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });

  if (!sessionToken) {
    return {
      authenticated: false,
      redirectTo: "/auth/signin",
    };
  }

  if (sessionToken.error === "RefreshAccessTokenError") {
    return {
      authenticated: false,
      redirectTo: "/auth/signin?error=session_expired",
    };
  }

  const roles = sessionToken.roles || [];
  const userHasRequiredRole = hasRequiredRole(roles);

  if (!userHasRequiredRole) {
    return {
      authenticated: false,
      redirectTo: "/auth/signin?error=insufficient_role",
    };
  }

  return {
    authenticated: true,
    user: {
      id: sessionToken.sub!,
      email: sessionToken.email,
      name: sessionToken.name,
      roles,
      hasRequiredRole: userHasRequiredRole,
    },
  };
}
