"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { signOut, useSession } from "next-auth/react";
import { formatEmailDate, formatEmailDateTime } from "@/lib/date-utils";
import {
  EmailMessage,
  EmailListResponse,
  EmailDetailResponse,
} from "@/types/email";

interface EmailListProps {
  emails?: EmailMessage[]; // 改为可选，支持客户端加载
}

export default function EmailList({ emails: initialEmails }: EmailListProps) {
  const [emails, setEmails] = useState<EmailMessage[]>(initialEmails || []);
  const [selectedEmail, setSelectedEmail] = useState<EmailMessage | null>(null);
  const [emailBody, setEmailBody] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [emailsLoading, setEmailsLoading] = useState(!initialEmails); // 如果没有初始数据，则显示加载状态
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { data: session } = useSession();

  // 加载邮件列表
  const loadEmails = async () => {
    if (!session) return;

    setEmailsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/emails");
      const data: EmailListResponse = await response.json();

      if (data.success) {
        // 兼容不同的 API 响应格式
        const emails = data.emails || data.messages || [];
        setEmails(emails);
      } else {
        setError(data.error || "加载邮件失败");
      }
    } catch (error) {
      console.error("Error loading emails:", error);
      setError("网络错误，请重试");
    } finally {
      setEmailsLoading(false);
    }
  };

  // 加载邮件内容
  const loadEmailBody = async (emailId: string) => {
    try {
      const response = await fetch(`/api/emails/${emailId}`);
      const data: EmailDetailResponse = await response.json();

      if (data.success && data.body) {
        setEmailBody(data.body);
      }
    } catch (error) {
      console.error("Error loading email body:", error);
    }
  };

  // 客户端加载邮件数据
  useEffect(() => {
    if (session && !initialEmails) {
      loadEmails();
    }
  }, [session, initialEmails]);

  const handleEmailClick = (email: EmailMessage) => {
    setSelectedEmail(email);
    loadEmailBody(email.id);
  };

  const handleRefresh = () => {
    setLoading(true);
    loadEmails();
    setTimeout(() => setLoading(false), 1000);
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/auth/signin" });
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">邮件列表</h2>
          {session?.user?.name && (
            <p className="text-sm text-gray-600 mt-1">
              欢迎, {session.user.name}
            </p>
          )}
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            {loading ? "Loading..." : "Refresh"}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Email List */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Emails ({emails.length})
            </h3>
            {error && <p className="text-sm text-red-600 mt-2">{error}</p>}
          </div>
          {emailsLoading ? (
            <div className="px-4 py-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">加载邮件中...</p>
            </div>
          ) : error ? (
            <div className="px-4 py-8 text-center">
              <p className="text-sm text-gray-500 mb-4">加载失败</p>
              <button
                onClick={loadEmails}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm"
              >
                重试
              </button>
            </div>
          ) : emails.length === 0 ? (
            <div className="px-4 py-8 text-center">
              <p className="text-sm text-gray-500">暂无邮件</p>
            </div>
          ) : (
            <ul className="divide-y divide-gray-200 max-h-[600px] overflow-y-auto">
              {emails.map((email) => (
                <li
                  key={email.id}
                  className={`px-4 py-4 hover:bg-gray-50 cursor-pointer ${
                    selectedEmail?.id === email.id ? "bg-blue-50" : ""
                  }`}
                  onClick={() => handleEmailClick(email)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center">
                        <p
                          className={`text-sm font-medium text-gray-900 truncate ${
                            !email.isRead ? "font-bold" : ""
                          }`}
                        >
                          {email.subject}
                        </p>
                        {!email.isRead && (
                          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            New
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 truncate">
                        From: {email.from}
                      </p>
                      <p className="text-sm text-gray-400 truncate">
                        {email.snippet}
                      </p>
                    </div>
                    <div className="ml-2 flex-shrink-0 text-sm text-gray-500">
                      {formatEmailDate(email.date)}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* Email Detail */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          {selectedEmail ? (
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                {selectedEmail.subject}
              </h3>
              <div className="mb-4 text-sm text-gray-600">
                <p>
                  <strong>From:</strong> {selectedEmail.from}
                </p>
                <p>
                  <strong>To:</strong> {selectedEmail.to}
                </p>
                <p>
                  <strong>Date:</strong>{" "}
                  {formatEmailDateTime(selectedEmail.date)}
                </p>
              </div>
              <div className="border-t pt-4">
                <div
                  className="prose max-w-none text-sm"
                  dangerouslySetInnerHTML={{
                    __html: emailBody || selectedEmail.snippet,
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="px-4 py-5 sm:p-6 text-center text-gray-500">
              Select an email to view its content
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
