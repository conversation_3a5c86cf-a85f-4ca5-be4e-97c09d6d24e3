# Production Environment Configuration Example
# Copy this file to .env.local on your production server and update the values

# Frontend (public) - Required for keycloak-js
NEXT_PUBLIC_KEYCLOAK_URL=https://keycloak.techexpresser.com
NEXT_PUBLIC_KEYCLOAK_REALM=dev
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=one-mail-dev
NEXT_PUBLIC_REQUIRED_ROLE=one-mail-admin

# Server - Required for API authentication
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=dev
KEYCLOAK_ISSUER_URL=https://keycloak.techexpresser.com/realms/dev
KEYCLOAK_CLIENT_ID=one-mail-dev
KEYCLOAK_CLIENT_SECRET=your-keycloak-client-secret
KEYCLOAK_ROLE_CLIENT_ID=one-mail-dev
KEYCLOAK_REQUIRED_ROLE=one-mail-admin

# Application Configuration
PORT=9042
# IMPORTANT: Change these URLs to match your production domain
# For example, if your domain is example.com:
# NEXTAUTH_URL=https://example.com
# NEXT_PUBLIC_BASE_URL=https://example.com
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-secure-nextauth-secret-key-for-production
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# Database Configuration (if using PostgreSQL)
DATABASE_URL=postgresql://myuser:mypassword@localhost:5432/one-mail-prod
PGHOST=localhost
PGPORT=5432
PGDATABASE=one-mail-prod
PGUSER=myuser
PGPASSWORD=mypassword

# Gmail API Configuration (if still needed)
# Note: Gmail credentials are stored in cert/google_oauth2.json and cert/token.json
