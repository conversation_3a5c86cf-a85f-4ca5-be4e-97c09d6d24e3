import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/jwt-middleware';
import { getAuthorizedClient, listMessagesForAddress } from '@/lib/gmail';

async function handleGetEmailsForAddress(request: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return NextResponse.json(
        { success: false, error: '邮箱地址参数缺失' },
        { status: 400 }
      );
    }

    // 获取Gmail授权客户端
    const auth = await getAuthorizedClient();
    const messages = await listMessagesForAddress(auth, email, 50); // 获取最多50封邮件

    return NextResponse.json({ success: true, emails: messages });
  } catch (error) {
    console.error('Error fetching emails for address:', error);
    return NextResponse.json(
      { success: false, error: '获取邮件失败' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return with<PERSON>uth(request, handleGetEmailsForAddress);
}
