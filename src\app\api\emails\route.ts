import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/jwt-middleware';
import { getAuthorizedClient, listMessages } from '@/lib/gmail';

async function handleGetEmails(request: AuthenticatedRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const maxResults = parseInt(searchParams.get('maxResults') || '10');

    // 这里你可以使用 request.user 来获取用户信息
    // 目前保持使用 Gmail API，但你可以根据需要修改
    const auth = await getAuthorizedClient();
    const messages = await listMessages(auth, maxResults);

    return NextResponse.json({ success: true, messages });
  } catch (error) {
    console.error('Error fetching emails:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch emails' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return withAuth(request, handleGetEmails);
}
