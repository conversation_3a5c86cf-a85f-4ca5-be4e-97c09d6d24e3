import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";

export default async function TestAuthPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/signin");
  }

  if (session.error === "RefreshAccessTokenError") {
    redirect("/auth/signin?error=session_expired");
  }

  if (!session.hasRequiredRole) {
    redirect("/auth/signin?error=insufficient_role");
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            认证测试页面
          </h1>
          
          <div className="space-y-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-700">用户信息</h2>
              <div className="mt-2 bg-gray-50 p-4 rounded">
                <p><strong>姓名:</strong> {session.user?.name || "未知"}</p>
                <p><strong>邮箱:</strong> {session.user?.email || "未知"}</p>
                <p><strong>ID:</strong> {session.user?.id || "未知"}</p>
              </div>
            </div>

            <div>
              <h2 className="text-lg font-semibold text-gray-700">角色信息</h2>
              <div className="mt-2 bg-gray-50 p-4 rounded">
                <p><strong>用户角色:</strong></p>
                <ul className="list-disc list-inside mt-1">
                  {session.roles?.map((role, index) => (
                    <li key={index} className="text-sm text-gray-600">
                      {role}
                    </li>
                  )) || <li className="text-sm text-gray-600">无角色</li>}
                </ul>
                <p className="mt-2">
                  <strong>有必需角色:</strong> 
                  <span className={`ml-2 px-2 py-1 rounded text-sm ${
                    session.hasRequiredRole 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {session.hasRequiredRole ? "是" : "否"}
                  </span>
                </p>
                <p className="mt-1 text-sm text-gray-600">
                  <strong>必需角色:</strong> {process.env.KEYCLOAK_REQUIRED_ROLE}
                </p>
              </div>
            </div>

            <div>
              <h2 className="text-lg font-semibold text-gray-700">Token 信息</h2>
              <div className="mt-2 bg-gray-50 p-4 rounded">
                <p><strong>Access Token:</strong> 
                  <span className="text-sm text-gray-600 ml-2">
                    {session.accessToken ? "存在" : "不存在"}
                  </span>
                </p>
                <p><strong>Refresh Token:</strong> 
                  <span className="text-sm text-gray-600 ml-2">
                    {session.refreshToken ? "存在" : "不存在"}
                  </span>
                </p>
                <p><strong>ID Token:</strong> 
                  <span className="text-sm text-gray-600 ml-2">
                    {session.idToken ? "存在" : "不存在"}
                  </span>
                </p>
                {session.error && (
                  <p><strong>错误:</strong> 
                    <span className="text-red-600 ml-2">{session.error}</span>
                  </p>
                )}
              </div>
            </div>

            <div className="flex space-x-4 pt-4">
              <a
                href="/"
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                返回首页
              </a>
              <a
                href="/api/auth/signout"
                className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
              >
                登出
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
